import React, { useState, useEffect } from 'react';
import { Property, PropertyStats, PropertiesResponse, PropertyPagination } from '@/utils/types/property';
import PropertyService from '@/lib/propertyService';
import PropertyHeader from './PropertyHeader';
import PropertyFilters from './PropertyFilters';
import PropertyTable from './PropertyTable';
import { useToast } from '@/components/reusable/Notify';
import { showMessage } from '@/app/lib/Alert';

const PropertiesManagement: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PropertyPagination | null>(null);
  const [loading, setLoading] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('All Types');
  const [statusFilter, setStatusFilter] = useState('All Status');
  const [locationFilter, setLocationFilter] = useState('All Emirates');
  const [listingTypeFilter, setListingTypeFilter] = useState('All Types');

  // Sorting states
  const [sortBy, setSortBy] = useState('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');


  useEffect(() => {
    const fetchProperties = async () => {
      setLoading(true);
      try {
        const propertyService = new PropertyService();
        const response = await propertyService.getProperties();
        console.log('API Response:', response);
        if (response.success) {
          console.log('Properties received:', response.data.properties);
          setProperties(response.data.properties);
          setPagination(response.data.pagination);
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
        // For now, keep mock data as fallback
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, []);

  // Filter properties
  const filteredProperties = properties.filter(property => {
    const searchValue = searchTerm.toLowerCase();
    const matchesSearch = (property.title?.toLowerCase().includes(searchValue) ?? false) ||
                         (property.name?.toLowerCase().includes(searchValue) ?? false) ||
                         (property.location_name?.toLowerCase().includes(searchValue) ?? false) ||
                         (property.agentName?.toLowerCase().includes(searchValue) ?? false);

    const matchesType = typeFilter === 'All Types' || property.type === typeFilter;
    const matchesStatus = statusFilter === 'All Status' || property.status === statusFilter;
    const matchesLocation = locationFilter === 'All Emirates' || property.emirate === locationFilter;
    const matchesListingType = listingTypeFilter === 'All Types' ||
                         property.listing_type?.toString() === listingTypeFilter;

    return matchesSearch && matchesType && matchesStatus && matchesLocation && matchesListingType;
  });

  // Sort properties
  const sortedProperties = [...filteredProperties].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    // Handle field name mapping
    if (sortBy === 'title') {
      aValue = a.name || a.title || '';
      bValue = b.name || b.title || '';
    } else if (sortBy === 'expiryDate') {
      aValue = a.expiryDate || a.expiry_date || '';
      bValue = b.expiryDate || b.expiry_date || '';
    } else {
      aValue = a[sortBy as keyof Property];
      bValue = b[sortBy as keyof Property];
    }

    if (sortBy === 'price') {
      aValue = Number(aValue);
      bValue = Number(bValue);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Calculate stats
  const stats: PropertyStats = {
    totalProperties: properties.length,
    activeProperties: properties.filter(p => p.status === 'Available').length,
    pendingProperties: properties.filter(p => p.status === 'Unpublished').length,
    blockedProperties: properties.filter(p => p.status === 'Blocked').length
  };

  // Check if filters are applied
  const hasFilters = searchTerm !== '' || typeFilter !== 'all' || statusFilter !== 'all' ||
                    locationFilter !== 'all' || listingTypeFilter !== 'all';

  // Event handlers
  const handleSelectProperty = (propertyId: number) => {
    const idStr = propertyId.toString();
    setSelectedProperties(prev =>
      prev.includes(idStr)
        ? prev.filter(id => id !== idStr)
        : [...prev, idStr]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedProperties(checked ? sortedProperties.map(p => p.id.toString()) : []);
  };

  const handleDeleteProperty = async (propertyId: number) => {
    try {
      const propertyService = new PropertyService();
      const response = await propertyService.deleteProperty(propertyId);

      if (response.success) {
        // Remove from local state
        const idStr = propertyId.toString();
        setProperties(prev => prev.filter(p => p.id !== propertyId));
        setSelectedProperties(prev => prev.filter(id => id !== idStr));
        // You can add toast notification here if needed
      } else {
        console.error('Failed to delete property:', response.message);
        // You can show error toast here
      }
    } catch (error) {
      console.error('Error deleting property:', error);
      // You can show error toast here
    }
  };

  const handleBlockProperty = (propertyId: number) => {
    setProperties(prev => prev.map(p =>
      p.id === propertyId ? { ...p, status: 'Blocked' } : p
    ));
  };

  const handleStatusChange = async (propertyId: number, status: Property['status'], reason?: string) => {
    try {
      // Convert frontend status to backend format (lowercase)
      const backendStatus = status?.toLowerCase();



      // Validate that reason is provided for blocking
      if (backendStatus === 'blocked' && !reason?.trim()) {
        showMessage('A reason is required when blocking a property.', 'error');
        return;
      }

      // Call the API to update status on backend
      const { updatePropertyStatus } = await import('@/lib/propertyService');
      const result = await updatePropertyStatus(propertyId.toString(), backendStatus || '', reason);

      if (result.success) {
        // Update local state only if API call succeeds
        setProperties(prev => prev.map(p =>
          p.id === propertyId ? { ...p, status, status_name: status } : p
        ));

        // Show success notification
        showMessage(`Property status updated to ${status} successfully.`, 'success');
      } else {
        // Show error notification
        showMessage('Failed to update property status. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error updating property status:', error);
      showMessage('An error occurred while updating property status.', 'error');
    }
  };

  const handleAddNote = async (propertyId: number, note: string) => {
    try {
      const propertyService = new PropertyService();
      const result = await propertyService.addNote(propertyId, note);

      if (result.success) {
        showMessage('Note added successfully to the property.', 'success');
      } else {
        showMessage('Failed to add note. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error adding note to property:', error);
      showMessage('An error occurred while adding the note.', 'error');
    }
  };

  const handleBulkDelete = () => {
    const selectedIds = selectedProperties.map(id => parseInt(id));
    setProperties(prev => prev.filter(p => !selectedIds.includes(p.id)));
    setSelectedProperties([]);
  };

  const handleBulkBlock = () => {
    const selectedIds = selectedProperties.map(id => parseInt(id));
    setProperties(prev => prev.map(p =>
      selectedIds.includes(p.id) ? { ...p, status: 'Blocked' } : p
    ));
    setSelectedProperties([]);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setTypeFilter('All Types');
    setStatusFilter('All Status');
    setLocationFilter('All Emirates');
    setListingTypeFilter('All Types');
  };

  return (
    <div className="space-y-6">
      <PropertyHeader
        totalProperties={stats.totalProperties}
        activeProperties={stats.activeProperties}
        pendingProperties={stats.pendingProperties}
        blockedProperties={stats.blockedProperties}
      />

      <PropertyFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        typeFilter={typeFilter}
        setTypeFilter={setTypeFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        locationFilter={locationFilter}
        setLocationFilter={setLocationFilter}
        listingTypeFilter={listingTypeFilter}
        setListingTypeFilter={setListingTypeFilter}
        filteredCount={filteredProperties.length}
        totalCount={properties.length}
        hasFilters={hasFilters}
        onClearFilters={handleClearFilters}
      />

      <PropertyTable
        properties={sortedProperties}
        selectedProperties={selectedProperties}
        onSelectProperty={handleSelectProperty}
        onSelectAll={handleSelectAll}
        onDeleteProperty={handleDeleteProperty}
        onBlockProperty={handleBlockProperty}
        onStatusChange={handleStatusChange}
        onAddNote={handleAddNote}
        onBulkDelete={handleBulkDelete}
        onBulkBlock={handleBulkBlock}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSort={handleSort}
      />
    </div>
  );
};

export default PropertiesManagement;
