import React, { useState } from 'react';
import { MoreH<PERSON>zontal, Eye, Edit, Ban, Trash2, Star, CheckCircle, MessageSquare } from 'lucide-react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import PropertyStatusBadge from './PropertyStatusBadge';
import PropertyTypeBadge from './PropertyTypeBadge';
import PropertyActionReasonDialog from './PropertyActionReasonDialog';
import AddNoteToPropertyDialog from './AddNoteToPropertyDialog';
import { Property } from '@/utils/types/property';

interface PropertyTableRowProps {
    property: Property;
    isSelected: boolean;
    onSelect: () => void;
    onDelete: () => void;
    onBlock: () => void;
    onStatusChange: (status: Property['status'], reason?: string) => void;
    onAddNote?: (propertyId: number, note: string) => void;
}

const PropertyTableRow: React.FC<PropertyTableRowProps> = ({ property, isSelected, onSelect, onDelete, onBlock, onStatusChange, onAddNote }) => {
    const [reasonDialogOpen, setReasonDialogOpen] = useState(false);
    const [actionType, setActionType] = useState<'block' | 'unpublish'>('block');
    const [addNoteDialogOpen, setAddNoteDialogOpen] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

    const formatPrice = (price: string | number) => {
        const numPrice = typeof price === 'string' ? parseFloat(price) : price;
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'AED',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(numPrice);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };



    const handleReasonConfirm = (reason: string) => {
        console.log('handleReasonConfirm called with reason:', reason, 'actionType:', actionType);
        if (actionType === 'block') {
            onStatusChange('blocked', reason);
        } else {
            onStatusChange('unpublished', reason);
        }
        setReasonDialogOpen(false);
    };

    // Unified status change handler that intercepts blocking actions
    const handleStatusChangeRequest = (newStatus: Property['status']) => {
        const normalizedStatus = newStatus?.toLowerCase();

        // If trying to block or unpublish, show reason dialog
        if (normalizedStatus === 'blocked') {
            setActionType('block');
            setReasonDialogOpen(true);
        } else if (normalizedStatus === 'unpublished') {
            setActionType('unpublish');
            setReasonDialogOpen(true);
        } else {
            // For other status changes, proceed directly
            onStatusChange(newStatus);
        }
    };

    const handleAddNoteClick = () => {
        setAddNoteDialogOpen(true);
    };

    const handleAddNoteConfirm = (propertyId: number, note: string) => {
        if (onAddNote) {
            onAddNote(propertyId, note);
        }
        setAddNoteDialogOpen(false);
    };

    return (
        <>
            <TableRow>
                <TableCell>
                    <Checkbox checked={isSelected} onCheckedChange={onSelect} />
                </TableCell>
                <TableCell>
                    <div className="flex items-center gap-3">
                        <img src={property.images?.[0]?.url || '/default.png'} alt={property.name || property.title || 'Property'} className="h-12 w-12 rounded-lg object-cover" />
                        <div>
                            <div className="flex items-center gap-2 font-medium">
                                {property.name || property.title || 'Untitled Property'}
                                {property.featured && <Star className="h-4 w-4 fill-current text-yellow-500" />}
                                {property.verified && <CheckCircle className="h-4 w-4 text-green-500" />}
                            </div>
                            <div className="text-sm text-gray-500">ID: {property.id}</div>
                        </div>
                    </div>
                </TableCell>
                <TableCell>
                    <PropertyTypeBadge type={property.type} />
                </TableCell>
                <TableCell className="font-semibold">{formatPrice(property.price)}</TableCell>
                <TableCell>{property.location_name || 'N/A'}</TableCell>
                <TableCell>
                    <div>
                        <div className="font-medium">{property.agencyName}</div>
                    </div>
                </TableCell>
                <TableCell>
                    <PropertyStatusBadge status={property.status} onStatusChange={handleStatusChangeRequest} />
                </TableCell>
                <TableCell>
                    <div className="text-sm">{property.expiryDate ? formatDate(property.expiryDate) : 'N/A'}</div>
                </TableCell>
                <TableCell>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-auto px-2 text-black hover:bg-gray-50">
                                •••
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="z-[9998] min-w-[200px] border bg-white shadow-lg">
                            <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                            </DropdownMenuItem>
                            {/* <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Property
                            </DropdownMenuItem> */}
                            <DropdownMenuItem onClick={() => handleStatusChangeRequest('unpublished')}>
                                <Ban className="mr-2 h-4 w-4" />
                                Unpublish Property
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleStatusChangeRequest('blocked')}>
                                <Ban className="mr-2 h-4 w-4" />
                                Block Property
                            </DropdownMenuItem>
                            {onAddNote && (
                                <DropdownMenuItem onClick={handleAddNoteClick}>
                                    <MessageSquare className="mr-2 h-4 w-4" />
                                    Add Note
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => setDeleteDialogOpen(true)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Property
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </TableCell>
            </TableRow>

            <PropertyActionReasonDialog isOpen={reasonDialogOpen} onClose={() => setReasonDialogOpen(false)} onConfirm={handleReasonConfirm} property={property} actionType={actionType} />

            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Property</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete "{property.name || property.title || 'this property'}"? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
                        <Button variant="destructive" onClick={() => {
                            console.log('Delete button clicked for property:', property.id);
                            onDelete();
                            setDeleteDialogOpen(false);
                        }}>Delete</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {onAddNote && addNoteDialogOpen && (
                <AddNoteToPropertyDialog
                    property={property}
                    onAddNote={handleAddNoteConfirm}
                    open={addNoteDialogOpen}
                    onOpenChange={setAddNoteDialogOpen}
                />
            )}
        </>
    );
};

export default PropertyTableRow;
