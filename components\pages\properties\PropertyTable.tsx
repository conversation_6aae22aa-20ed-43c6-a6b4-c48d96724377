import React from 'react';
import { Eye, Trash2, Ban } from 'lucide-react';
import DataTable from '@/components/shared/DataTable';
import BulkActionsCard from '@/components/shared/BulkActionsCard';
import PropertyTableRow from './PropertyTableRow';
import PropertyTableHeaders from './PropertyTableHeaders';
import { Property } from '@/utils/types/property';

interface PropertyTableProps {
  properties: Property[];
  selectedProperties: string[];
  onSelectProperty: (propertyId: number) => void;
  onSelectAll: (checked: boolean) => void;
  onDeleteProperty: (propertyId: number) => void;
  onBlockProperty: (propertyId: number) => void;
  onStatusChange: (propertyId: number, status: Property['status'], reason?: string) => void;
  onAddNote?: (propertyId: number, note: string) => void;
  onBulkDelete: () => void;
  onBulkBlock: () => void;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSort: (field: string) => void;
}

const PropertyTable: React.FC<PropertyTableProps> = ({
  properties,
  selectedProperties,
  onSelectProperty,
  onSelectAll,
  onDeleteProperty,
  onBlockProperty,
  onStatusChange,
  onAddNote,
  onBulkDelete,
  onBulkBlock,
  sortBy,
  sortOrder,
  onSort
}) => {
  const bulkActions = [
    {
      label: 'Delete Selected',
      action: onBulkDelete,
      variant: 'destructive' as const,
      icon: Trash2
    },
    {
      label: 'Block Selected',
      action: onBulkBlock,
      variant: 'secondary' as const,
      icon: Ban
    }
  ];

  const emptyState = {
    icon: <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4 mx-auto"><Eye className="w-6 h-6 text-gray-400" /></div>,
    title: 'No properties found',
    description: 'No properties match your current filters. Try adjusting your search criteria.'
  };

  const headers = PropertyTableHeaders({ sortBy, sortOrder, onSort });

  return (
    <div className="space-y-4">
      {selectedProperties.length > 0 && (
        <BulkActionsCard
          selectedCount={selectedProperties.length}
          actions={bulkActions}
        />
      )}

      <DataTable
        headers={headers}
        selectedItems={selectedProperties}
        totalItems={properties.length}
        onSelectAll={onSelectAll}
        emptyState={emptyState}
      >
        {properties.map((property) => (
          <PropertyTableRow
            key={property.id}
            property={property}
            isSelected={selectedProperties.includes(property.id.toString())}
            onSelect={() => onSelectProperty(property.id)}
            onDelete={() => {
                console.log('PropertyTable onDelete called for property:', property.id);
                onDeleteProperty(property.id);
            }}
            onBlock={() => onBlockProperty(property.id)}
            onStatusChange={(status, reason) => onStatusChange(property.id, status, reason)}
            onAddNote={onAddNote}
          />
        ))}
      </DataTable>
    </div>
  );
};

export default PropertyTable;
